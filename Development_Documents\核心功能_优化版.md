# 核心功能（优化版）

## 参数设置功能

### 左/右翻转步进电机参数
左/右翻转电机是两个独立的电机，需要独立的线程控制，功能相同。
1. 固定参数：细分为10000pulses/r，带1:3减速机
2. 动作参数：初速度、加速度、加速时间等运动控制参数
3. 示教界面：相对角度移动、当前角度显示、归零功能、5组位置保存和测试功能

### 输入/输出皮带电机参数
输入/输出皮带电机也是两个独立的步进电机，需要独立的线程控制，只控制速度，不需要精确角度控制。
1. 固定参数：细分为10000pulses/r，皮带滚轮直径为5cm
2. 运行参数：初速度、加速时间、最大加速度、最大速度

## 扫码器参数设置
3个独立的扫码器，每个扫码器需要设置：串口端口、波特率、数据位、停止位、奇偶校验
- 出厂设置：波特率115200、数据位8、停止位1、奇偶校验无
- 串口端口需要永久保存

## IO逻辑特性

### 输入IO逻辑（低电平有效）
- I0005,I0007-I0012（位置传感器）：0=到位，1=未到位
- I0006,I0010（取料完成传感器）：0=取料完成，1=取料未完成
- I0104,I0105（NG品放置传感器）：0=可以放置，1=不可放置
- I0106（OK品放置传感器）：1=可以放置，0=不可放置

### 输出IO逻辑（低电平有效）
- 硬件物理特性：0=激活设备，1=关闭设备
- 业务代码：false激活设备，true关闭设备

## 程序启动和初始化
1. 程序启动后加载所有UI界面
2. 系统自动初始化：
   - 运动控制卡（dmc1000b）初始化
   - 左/右翻转电机自动回零并移动到位置1
   - 自动连接2台6轴机器人的主端口和数据端口
   - 初始化完成后程序进入待机状态（AutoRun_status=4）
3. 待机状态功能：
   - 支持参数修改和保存
   - 支持电机位置示教
   - 支持手动调试各种动作
   - 支持IO状态查看和手动控制
   - 等待用户按下启动按钮开始自动化程序

## 全局静态字段（第三方通信接口）

**注意事项：**
- 必须实现线程安全封装
- 需要提供状态变更通知机制
- 严格遵循"谁读取，谁复位"原则

**bool字段复位原则：**
- 所有bool类型字段在业务逻辑执行完成后必须立即复位为false
- AutoRun_status不是bool字段，与系统状态同步，不需要复位
- 特殊情况：L_robotget_ok/R_robotget_ok由系统发送完扫码数据后自动复位
- 异常情况下也要确保相关状态正确复位

### 通信字段定义：
- 电机准备状态：L_moto_ready, R_moto_ready
- 位置到达状态：L_position_Arrived, R_position_Arrived
- 夹爪状态：L_gripper_ok, R_gripper_ok
- 角度矫正状态：L_Angle_ok, R_Angle_ok
- 安全距离状态：L_safe_ok, R_safe_ok
- 扫码触发标志：L_scan_trigger, R_scan_trigger
- 扫码完成标志：L_scan_completed, R_scan_completed
- 扫描器数据状态：L_trigger_ok, R_trigger_ok, ML_trigger_ok, MR_trigger_ok
- 机器人取料权限：L_robotget_ok, R_robotget_ok
- 拍照触发：Tack_picture
- 自动运行状态：AutoRun_status（0=停止，1=运行中，2=暂停中，3=复位，4=待机）

### 扫码器数据保存字符串：
- 左扫码器数据：string L_Scanner_Data（保存1号扫码器返回的产品信息）
- 右扫码器数据：string R_Scanner_Data（保存2号扫码器返回的产品信息）
- 中间向左扫码器数据：string ML_Scanner_Data（保存3号扫码器向左方向的产品信息）
- 中间向右扫码器数据：string MR_Scanner_Data（保存3号扫码器向右方向的产品信息）

## 时间参数配置

**注意事项：**
- 所有等待时间必须通过配置文件管理
- 支持运行时动态调整
- 提供默认值和参数验证

### 时间参数类型：
- 夹爪动作延时：激活后等待时间（默认200ms）
- 气缸动作延时：伸出/缩回后等待时间（默认200ms/100ms）
- 扫码超时时间：单次扫码最大等待时间（默认2000ms）
- 机器人响应超时：通信指令最大等待时间（默认1000ms）
- 安全检查间隔：系统状态检查频率（默认50ms）
- IO状态检查间隔：传感器读取频率（默认10ms）
- 紧急停止响应时间：安全信号检测间隔（默认5ms）

## 皮带电机控制逻辑

### 输入皮带电机（独立线程）
- 监控传感器：I0004
- 控制逻辑：
  - 传感器为1（无产品）→ 皮带转动
  - 传感器为0（有产品）→ 皮带停止，Tack_picture置1
  - 拍照结束后立即复位Tack_picture为false，继续检查传感器状态

### 输出皮带电机（独立线程）
- 监控传感器：I0106
- 控制逻辑：
  - 传感器为1（无产品）→ 皮带停止
  - 传感器为0（有产品）→ 皮带转动

## 左/右翻转电机自动执行流程（2个独立线程）

### 流程步骤：
1. **初始化**：回原点，移动到位置1，电机准备标志置1
2. **夹紧动作**：等待位置到达信号→激活夹爪→检查夹紧状态→夹爪状态标志置1→复位L_position_Arrived/R_position_Arrived
3. **等待条件**：等待角度矫正完成和安全距离确认→复位L_Angle_ok/R_Angle_ok和L_safe_ok/R_safe_ok
4. **扫码流程**：移动到位置2→触发扫码（L_scan_trigger/R_scan_trigger置1）→等待扫码完成（超时处理）→复位L_scan_trigger/R_scan_trigger和L_scan_completed/R_scan_completed
5. **顶料动作**：移动到位置3→激活顶料气缸→检查到位状态→移动到位置4
6. **退料动作**：关闭顶料气缸→检查缩回状态
7. **取料准备**：移动到位置5→机器人取料权限置1（L_robotget_ok/R_robotget_ok）

### 注意事项：
- 位置2专门用于扫码
- 位置5专门用于机器人取料，不进行扫码
- 所有IO检查都要考虑低电平有效的特性
- 错误状态需要记录并触发相应的处理机制
- 严格遵循复位原则：所有bool状态在业务执行完成后立即复位

## 扫码器自动模式功能

### 通信协议：
- 数据格式：ASCII
- 触发指令：发送"start"
- 连接检查：发送"hello"，回复"world"表示正常

### 自动流程：
1. **初始化**：程序启动时自动连接3个串口，发送连接检查
2. **中间扫码器**：
   - 等待ML_trigger_ok触发→发送扫码指令→保存结果到ML_Scanner_Data→扫码成功后立即复位ML_trigger_ok
   - 等待MR_trigger_ok触发→发送扫码指令→保存结果到MR_Scanner_Data→扫码成功后立即复位MR_trigger_ok
3. **左右扫码器**：
   - 左扫码器：等待L_scan_trigger触发→发送扫码指令→保存结果到L_Scanner_Data→设置L_scan_completed为1→扫码成功后立即复位L_scan_trigger
   - 右扫码器：等待R_scan_trigger触发→发送扫码指令→保存结果到R_Scanner_Data→设置R_scan_completed为1→扫码成功后立即复位R_scan_trigger

### 注意事项：
- 确保UI控件参数与业务逻辑正确对应
- 串口端口选择需要永久保存
- 扫码超时需要有明确的处理机制
- 扫码器复位机制：成功保存数据到对应字符串后，立即自动复位对应的触发标志
- 严格遵循复位原则：扫码相关bool状态在业务执行完成后立即复位

## 六轴机器人通信（2个独立线程）

### 通信架构：
- 主端口：指令收发（连接、登录、启动、停止）
- 数据端口：数据收发（取料权限、产品信息等）

### 通信协议：
1. **取料权限管理**：
   - 机器人发送"GETPICK"→判断扫码完成状态→回复"ALLOWPICK"或"DENYPICK"
   - 发送"ALLOWPICK"后，等待机器人发送"INPICK"

2. **取料执行**：
   - 机器人发送"INPICK"→松开夹爪→检查取料完成→发送产品信息
   - 产品信息格式：左机器人发送"ML_Scanner_Data,L_Scanner_Data"；右机器人发送"MR_Scanner_Data,R_Scanner_Data"
   - 发送完产品信息后，系统自动复位L_robotget_ok/R_robotget_ok
   - 复位L_gripper_ok/R_gripper_ok

3. **NG品放置**：
   - 机器人1发送"GETNGPUT"→检查I0104→回复"ALLOWNGPUT"或"DENYNGPUT"
   - 机器人2发送"GETNGPUT"→检查I0105→回复"ALLOWNGPUT"或"DENYNGPUT"
   - 机器人1发送"NGPUTFULL"→监控I0104状态变化（0→1再→0）→发送"RESETNGPUT"
   - 机器人2发送"NGPUTFULL"→监控I0105状态变化（0→1再→0）→发送"RESETNGPUT"

4. **OK品放置**：
   - 机器人1/机器人2发送"GETOKPUT"→检查I0106→回复"ALLOWOKPUT"或"DENYOKPUT"

### 注意事项：
- 通信超时需要有重连机制
- 网络异常要有错误恢复策略
- 严格遵循复位原则：机器人通信相关bool状态在业务执行完成后立即复位
- L_robotget_ok/R_robotget_ok由系统发送完扫码数据后自动复位

## 安全管理（独立线程）

### 紧急停止条件：
- I0102/I0103为0或I0101为1：紧急停止机器人
- I0003为0：紧急停止所有操作，必须为1才能重新启动

### 控制按钮：
- I0001电平变化：启动机器人
- I0002电平变化：停止程序和机器人，再次点击后程序复位继续执行

### 指示灯控制（低电平有效）：
- 程序待机状态：黄灯亮（O0010输出0）
- 自动化程序运行：绿灯亮（O0009输出0）
- 报错紧急停止：红灯亮（O0008输出0）
- 状态切换时先关闭原状态再点亮新状态

### 注意事项：
- 安全功能具有最高优先级
- 紧急停止响应时间要尽可能短
- 安全线程不能被其他业务逻辑干扰
- 紧急情况下要确保所有相关bool状态正确复位

## 系统状态管理

### 程序状态：
- 程序启动初始化：AutoRun_status=3（复位）
- 初始化完成：AutoRun_status=4（待机）
- 用户启动自动化：AutoRun_status=1（运行中）
- 用户暂停自动化：AutoRun_status=2（暂停中）
- 用户停止自动化：AutoRun_status=4（返回待机）
- 程序退出：AutoRun_status=0（停止）

### 控制器管理：
- 程序启动时创建所有必要的控制线程
- 在待机状态下线程处于等待状态
- 用户启动后开始自动化流程
- 停止时安全关闭所有线程和连接

### 待机状态功能要求：
- UI界面保持响应，用户可以操作所有设置界面
- 电机可以手动控制和示教位置
- IO状态实时显示和手动控制
- 所有参数修改都可以实时生效并永久保存

### 注意事项：
- 状态转换要有明确的条件判断
- 待机状态下要保持系统响应性
- 自动化程序启动前要检查所有设备就绪状态
- 异常情况要有恢复到待机状态的机制
- 用户在待机状态下的操作不能影响设备安全
- AutoRun_status与系统状态同步，不需要复位机制

## 错误处理原则

### 错误分级：
- 警告级：记录日志，继续运行
- 错误级：暂停操作，等待干预
- 严重级：紧急停止，激活安全机制

### 处理策略：
- 统一的错误记录和通知机制
- 明确的错误恢复流程
- 重要操作的状态回滚能力

### 注意事项：
- IO操作失败要有明确的错误信息
- 通信异常要有重试和降级机制
- 系统异常要能自动恢复到安全状态
- 错误处理过程中也要确保相关bool状态正确复位

